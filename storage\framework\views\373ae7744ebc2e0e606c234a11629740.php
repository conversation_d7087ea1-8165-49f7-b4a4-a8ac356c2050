<script>
  // Global variables
  var authorId, pageTitle, selectionBanner, smScreen, btnUpdate, btnLoading;
  var formContent, showContent, emptyContent;
  var status, filter, flag, detailContent;
  var contentCounts = {};
  var selectedIds = [];
  var selectedKeywords = [];
  var isMultipleSelect = false;
  var descriptionCounter;
  var itemData = {};
  var detailContentForm;
  var inputContentId;
  var keys = ["draft", "pending", "reviewed", "approved", "rejected", "published"];

  // Status URL mapping - moved to global scope to avoid initialization issues
  const statusUrlMap = {
    'Draft': 'not_submitted',
    'Pending': 'pending',
    'Rejected': 'rejected',
    'Approved': 'approved',
    'Published': 'published'
  };

  $(document).ready(function () {
    authorId = "<?php echo e(auth()->user()->id); ?>";
    pageTitle = $("#page-title");
    selectionBanner = $("#banner-container");

    // Adjust banner position based on sidebar width
    function adjustBannerPosition() {
      const windowWidth = $(window).width();

      if (windowWidth >= 992) { // Desktop view
        const sidebarWidth = $(".app-sidebar").width() || 280;
        selectionBanner.css("left", sidebarWidth + "px");
        selectionBanner.css("width", (windowWidth - sidebarWidth) + "px");
      } else { // Mobile view
        selectionBanner.css("left", "0");
        selectionBanner.css("width", "100%");
      }
      selectionBanner.css("z-index", "100");
    }
    
    smScreen = window.matchMedia("(max-width: 991px)").matches;
    btnUpdate = $("#group-btn-update");
    btnLoading = $(".btn-loading");
    formContent = `<?php echo $__env->make('pages.submit-content.form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>`;
    showContent = `<?php echo $__env->make('pages.submit-content.show', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>`;
    emptyContent = `<?php echo $__env->make('pages.submit-content.empty', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>`;

    // Get filter from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filterParam = urlParams.get('filter');
    
    // Set initial filter based on URL parameter or default to index
    filter = filterParam || "index";
    flag = "Reviewed";
    
    // Initialize content based on current page
    const path = window.location.pathname;
    if (path.includes('pending')) {
      status = 'Pending';
    } else if (path.includes('approved')) {
      status = 'Approved';
    } else if (path.includes('rejected')) {
      status = 'Rejected';
    } else if (path.includes('published')) {
      status = 'Published';
    } else {
      status = 'Draft';
    }
    
    detailContent = $("#detail-content");

    // Show the correct tab based on status
    showCorrectTab(status);

    // Activate the correct filter
    $(".filter-media").removeClass("active");
    $(`.filter-media[data-filter="${filter}"]`).addClass("active");

    // Update page title and badge
    updatePageTitle(status);
    $("#status-badge").text("0");
    $("#status-badge").removeClass('hidden');

    // Initialize banner position
    adjustBannerPosition();

    // Initialize sidebar menu active state
    function updateSidebarActiveState() {
      $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300 bg-gray-800");
      const currentTabParam = statusUrlMap[status];
      $(`#submit-content-submenu a[href*="tab=${currentTabParam}"]`).addClass("bg-gray-50 dark:bg-coal-300");
    }

    updateSidebarActiveState();

    // Add custom CSS to override the bullet point
    if (!$("#sidebar-bullet-fix").length) {
      $("head").append(`
        <style id="sidebar-bullet-fix">
          #submit-content-submenu .menu-link::before {
            opacity: 0 !important;
          }
          #submit-content-submenu .menu-link.bg-gray-50::before {
            opacity: 1 !important;
          }
        </style>
      `);
    }

    contentCounts = {
      draft: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      pending: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      reviewed: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      approved: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      rejected: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      published: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      }
    };

    function getContent(status, mediaType, author, url) {
      // Make sure the badge is visible with the current status color before the AJAX call
      updatePageTitle(status);

      $.ajax({
        url: url || "<?php echo e(route('get-content-author')); ?>",
        type: "GET",
        data: {
            'status': status,
            'media_type': mediaType,
            'author': author
        },
        beforeSend: function() {
          $(".loading-content").show();
          $(".toolbar-skeleton").show();
          $("#filter-menu").hide();
          $("#tab-note").hide();
          $("#flag-menu").hide();
        },
        success: function(response) {
          let count = response.data.count;

          // Log the count data for debugging
          console.log("Count data:", count);

          // Update counts for all statuses
          keys.forEach(key => {
            // Convert to lowercase for consistency
            const statusKey = key.toLowerCase();

            // Get the count values, defaulting to 0 if not present
            contentCounts[statusKey].index = parseInt(count[statusKey] || 0);
            contentCounts[statusKey].image = parseInt(count[`${statusKey}_image`] || 0);
            contentCounts[statusKey].video = parseInt(count[`${statusKey}_video`] || 0);
            contentCounts[statusKey].audio = parseInt(count[`${statusKey}_audio`] || 0);
            contentCounts[statusKey].document = parseInt(count[`${statusKey}_document`] || 0);

            console.log(`Status: ${statusKey}, Count: ${contentCounts[statusKey].index}`);
          });

          // Special case: For Approved tab, include Published content count
          // This is because in the Approved tab, we show both Approved and Published content
          if (status === "Approved") {
            contentCounts.approved.index = parseInt(count.approved || 0) + parseInt(count.published || 0);
            contentCounts.approved.image = parseInt(count.approved_image || 0) + parseInt(count.published_image || 0);
            contentCounts.approved.video = parseInt(count.approved_video || 0) + parseInt(count.published_video || 0);
            contentCounts.approved.audio = parseInt(count.approved_audio || 0) + parseInt(count.published_audio || 0);
            contentCounts.approved.document = parseInt(count.approved_document || 0) + parseInt(count.published_document || 0);

            console.log("Updated Approved count:", contentCounts.approved.index);
          }

          // Update the page title and badge immediately after getting the counts
          updatePageTitle(status);

          // Log the current status count for debugging
          console.log("Current status count:", contentCounts[status.toLowerCase()].index);

          $("#tab-note").show();
          $("#filter-menu").show();
          $(".toolbar-skeleton").hide();
          $(".loading-content").hide();
          contentData(response.data.content)

          // Always update filter for all statuses
          updateFilter(filter, status);

          // if(["Reviewed", "Approved", "Rejected"].includes(status)){
          if(["Reviewed"].includes(status)){
            $("#flag-menu").show();
            updateFlag(status, filter)
          }
        },
        error: function(status, error) {
            console.log("Error:", status, error);
        }
      });
    }

    function updateFilter(filter, status) {
      let filterMap = {
        index: { name: "Semua", icon: "fa-list" },
        image: { name: "Image", icon: "fa-image" },
        video: { name: "Video", icon: "fa-video" },
        audio: { name: "Audio", icon: "fa-music" },
        document: { name: "Document", icon: "fa-file" },
      };

      let pageNote = {
        draft: { note: "Need To Be Submitted"},
        pending: { note: "Waiting To Be Reviewed"},
        reviewed: { note: "Have Been Reviewed"},
        rejected: { note: "Have Been Rejected"},
        approved: { note: "Have Been Approved"},
        published: { note: "Are Published"}
      }

      let statusKey = status.toLowerCase();
      let filterKey = filter.toLowerCase();

      // Make sure contentCounts[statusKey] exists
      if (!contentCounts[statusKey]) {
        contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      let count = contentCounts[statusKey][filterKey] ?? 0;

      $("#btn-filter").html(`
        <span class="flex items-center me-1">
         <i class="fa-solid ${filterMap[filterKey].icon}"></i>
        </span>
        <span class="hidden md:inline text-nowrap">
         ${filterMap[filterKey].name} (${count})
        </span>
        <span class="inline md:hidden text-nowrap">
         ${filterMap[filterKey].name} (${count})
        </span>
        <span class="flex items-center lg:ms-4">
         <i class="ki-filled ki-down !text-xs"></i>
        </span>
      `);

      ["index", "image", "video", "audio", "document"].forEach(media => {
        let mediaCount = contentCounts[statusKey][media] ?? "0";
        $(`#filter-item-${media}`).text(`${filterMap[media].name} (${mediaCount})`);
      });

      keys.forEach(key => {
        // Only show count if greater than 0
        const count = parseInt(contentCounts[key].index) || 0;
        if (count > 0) {
          $(`#tab-item-${key}`).text(count);
        } else {
          $(`#tab-item-${key}`).text("");
        }
      });

      // Get count for current status
      const statusCount = parseInt(contentCounts[statusKey].index) || 0;

      // Update the tab note with count
      if (statusCount > 0) {
        $('#tab-note').text(statusCount + " File(s) " + pageNote[statusKey].note);
      } else {
        $('#tab-note').text("0 File(s) " + pageNote[statusKey].note);
      }

      // Update the page title and badge when counts are updated
      updatePageTitle(status);
    }

    function updateFlag(flag, filter){
      let flagMap = {
          reviewed: { name: "Semua",},
          approved: { name: "Approved",},
          rejected: { name: "Rejected",}
      };

      let flagKey = flag.toLowerCase();
      let filterKey = filter.toLowerCase();

      // Make sure contentCounts[flagKey] exists
      if (!contentCounts[flagKey]) {
        contentCounts[flagKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      let count = contentCounts[flagKey][filterKey] ?? 0;

      $("#btn-flag").html(`
        <span class="hidden md:inline text-nowrap">
          ${flagMap[flagKey].name} (${count})
        </span>
        <span class="inline md:hidden text-nowrap">
          ${flagMap[flagKey].name} (${count})
        </span>
        <span class="flex items-center lg:ms-4">
          <i class="ki-filled ki-down !text-xs"></i>
        </span>
      `);

      ["reviewed", "approved", "rejected"].forEach(media => {
        let mediaCount = contentCounts[media][filterKey] ?? "0";
        $(`#flag-item-${media}`).text(`${flagMap[media].name} (${mediaCount})`);
      });
    }

    getContent(status, filter, authorId);

    // Simplified tab switching function
    function showCorrectTab(currentStatus) {
      const tabMap = {
        'Draft': '#tab_draft',
        'Pending': '#tab_pending',
        'Rejected': '#tab_rejected',
        'Approved': '#tab_approved',
        'Published': '#tab_published'
      };

      // Hide all tabs and show the correct one
      Object.values(tabMap).forEach(tab => $(tab).addClass("hidden"));

      const targetTab = tabMap[currentStatus];
      if (targetTab) {
        $(targetTab).removeClass("hidden");
      }
    }

    function updatePageTitle(currentStatus) {
      const statusTitleMap = {
        'Draft': 'Not Submitted',
        'Pending': 'Pending',
        'Rejected': 'Rejected',
        'Approved': 'Approved',
        'Published': 'Published'
      };

      const badgeClassMap = {
        'Draft': 'badge-outline badge-dark',
        'Pending': 'badge-outline badge-warning',
        'Rejected': 'badge-outline badge-danger',
        'Approved': 'badge-outline badge-primary',
        'Published': 'badge-outline badge-success'
      };

      // Update the title and badge
      $("#status-title").text("Submit Content - " + statusTitleMap[currentStatus]);

      // Get the count for the current status
      const statusKey = currentStatus.toLowerCase();

      // Make sure contentCounts[statusKey] exists
      if (!contentCounts[statusKey]) {
        contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      const count = parseInt(contentCounts[statusKey].index) || 0;

      // Log the count for debugging
      console.log("Status:", currentStatus, "Count:", count, "Full object:", contentCounts[statusKey]);

      // Update the badge count - always show the count, even if it's 0
      $("#status-badge").text(count);

      // Make sure the badge is visible
      $("#status-badge").removeClass('hidden');

      // Simplified badge color mapping
      const badgeColorMap = {
        'draft': 'badge-dark',
        'pending': 'badge-warning',
        'rejected': 'badge-danger',
        'approved': 'badge-primary',
        'published': 'badge-success'
      };
      const badgeClass = badgeColorMap[currentStatus.toLowerCase()] || 'badge-dark';

      // Update badge class - always keep the badge visible
      $("#status-badge").removeClass('badge-dark badge-warning badge-danger badge-primary badge-success hidden').addClass(badgeClass);

      // Update the badge text to always show "Approved" in the Approved tab
      if (currentStatus === "Approved") {
        $("#status-title").text("Submit Content - Approved");
      }

      // Show/hide the "Upload Now" button based on the current status
      // Only show it on the "Not Submitted" (Draft) tab
      if (currentStatus === 'Draft') {
        $("#upload-now-button").show();
      } else {
        $("#upload-now-button").hide();
      }

      // Note: Sidebar highlighting is handled by server-side rendering
      // No need for dynamic updates since we use full page refresh
    }

    // Initialize content on page load (removed duplicate call)
    // Note: getContent is already called on line 346

    // Simplified filter media click handler (removed duplicate)
    $(document).on("click", ".filter-media", function(){
      filter = $(this).data("filter");
      selectedIds = [];
      detailContent.html(emptyContent);
      btnUpdate.addClass("hidden").removeClass("flex");
      $(".filter-media").removeClass("active");
      $(this).addClass("active");

      // Update filter and get content
      updateFilter(filter, status);
      getContent(status, filter, authorId);

      // Update URL without reloading the page
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('filter', filter);
      window.history.pushState({}, '', newUrl);
    });

    $(document).on("click", ".flag", function(){
      flag = $(this).data("flag");
      selectedIds = [];
      detailContent.html(emptyContent)
      btnUpdate.addClass("hidden").removeClass("flex");
      $(".flag").removeClass("active");
      $(this).addClass("active");
      updateFlag(flag, filter);
      getContent(flag, filter, authorId);
    });

    // Event: Klik pada card untuk menampilkan content details
    $(document).on("click", ".select-card", function (e) {
        let card = $(this);
        let cardId = card.data("id");

        // Jika klik pada checkbox atau area sekitarnya, jangan lakukan apa-apa
        // Karena akan ditangani oleh handler checkbox
        if ($(e.target).closest('.multiple-select').length > 0 ||
            $(e.target).hasClass('unchecked') ||
            $(e.target).hasClass('checked')) {
            return;
        }

        // Jika dalam mode multiple select, klik pada card tidak akan mengubah seleksi checkbox
        // Hanya tampilkan detail content
        if (isMultipleSelect) {
            // Hanya tampilkan detail content tanpa mengubah seleksi
            detailContent.html(emptyContent);
            return;
        }

        // Jika klik di area card, hanya tampilkan detail tanpa mengubah checkbox
        handleShowDetails(card, cardId);
    });

    // Fungsi untuk menangani seleksi card
    function handleCardSelection(card, cardId) {
        if (!isMultipleSelect) {
            // Toggle single-select mode tanpa mengubah checkbox
            if (selectedIds.includes(cardId)) {
                updateSelection(card, cardId, false);
            } else {
                $(".select-card").each(function () {
                    updateSelection($(this), $(this).data("id"), false);
                });
                updateSelection(card, cardId, true);
            }
        } else {
            // Jika dalam mode multiple-select, tambahkan atau hapus dari daftar
            let isSelected = selectedIds.includes(cardId);
            updateSelection(card, cardId, !isSelected);

            // Hide content details when in multiple selection mode
            detailContent.html(emptyContent);
        }
    }

    // Fungsi untuk menampilkan detail content
    function handleShowDetails(card, cardId) {
        // Highlight the card visually without changing checkbox state
        $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
        card.addClass("border-blue-500").removeClass("border-gray-300");

        // Update selectedIds for tracking which item is being viewed
        // but don't update checkbox visual state
        selectedIds = [cardId];

        // Tampilkan detail content
        status == "Draft" ? updateForm() : updateShow();
    }

    // Fungsi untuk mengupdate seleksi card
    function updateSelection(card, cardId, isChecked) {
        let checkbox = card.find(".multiple-select");
        let uncheckedIcon = card.find(".unchecked");
        let checkedIcon = card.find(".checked");

        if (isChecked) {
            if (!selectedIds.includes(cardId)) {
                selectedIds.push(cardId);
            }
            card.addClass("border-blue-500").removeClass("border-gray-300");

            // Only update checkbox visual state when in multiple select mode
            // or when the checkbox itself was clicked
            if (isMultipleSelect) {
                checkbox.removeClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100").addClass("bg-neutral-900 opacity-100");
                uncheckedIcon.hide();
                checkedIcon.show();

                // Adjust banner position before showing it
                adjustBannerPosition();
                selectionBanner.show();
                if(smScreen) pageTitle.hide();
                $('#banner-title').text(selectedIds.length+" item dipilih");
            }
        } else {
            selectedIds = selectedIds.filter(id => id !== cardId);
            card.removeClass("border-blue-500").addClass("border-gray-300");

            // Only update checkbox visual state when in multiple select mode
            // or when the checkbox itself was clicked
            if (isMultipleSelect) {
                checkbox.removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
                uncheckedIcon.show();
                checkedIcon.hide();
                $('#banner-title').text(selectedIds.length+" item dipilih");
            }

            // Jika tidak ada card yang dipilih, kembali ke mode single-select
            if (selectedIds.length === 0) {
                isMultipleSelect = false;
                selectionBanner.hide();
                pageTitle.show();
            }
        }

        // Don't automatically update the form/show content here
        // This will be handled by the specific handlers
    }

    // Event: Klik pada checkbox multiple-select
    $(document).on("click", ".multiple-select, .unchecked, .checked", function (e) {
        e.stopPropagation(); // Hindari trigger klik pada card
        e.preventDefault(); // Prevent default behavior

        let card = $(this).closest(".select-card");
        let cardId = card.data("id");
        let isSelected = selectedIds.includes(cardId);

        // Always enter multiple selection mode when clicking on a checkbox
        isMultipleSelect = true;

        // Toggle selection state
        if (isSelected) {
            // Deselect
            selectedIds = selectedIds.filter(id => id !== cardId);
            card.removeClass("border-blue-500").addClass("border-gray-300");

            let checkbox = card.find(".multiple-select");
            let uncheckedIcon = card.find(".unchecked");
            let checkedIcon = card.find(".checked");

            checkbox.removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
            uncheckedIcon.show();
            checkedIcon.hide();
        } else {
            // Select
            selectedIds.push(cardId);
            card.addClass("border-blue-500").removeClass("border-gray-300");

            let checkbox = card.find(".multiple-select");
            let uncheckedIcon = card.find(".unchecked");
            let checkedIcon = card.find(".checked");

            checkbox.removeClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100").addClass("bg-neutral-900 opacity-100");
            uncheckedIcon.hide();
            checkedIcon.show();
        }

        // Update banner
        // Adjust banner position before showing it
        adjustBannerPosition();
        selectionBanner.show();
        if(smScreen) pageTitle.hide();
        $('#banner-title').text(selectedIds.length+" item dipilih");

        // If no items selected, exit multiple selection mode
        if (selectedIds.length === 0) {
            isMultipleSelect = false;
            selectionBanner.hide();
            if(smScreen) pageTitle.show();
        }

        // Hide content details when in multiple selection mode
        if (isMultipleSelect) {
            detailContent.html(emptyContent);
        }
    });

    // Event: Klik pada tombol delete
    $(document).on("click", ".btn-delete", function (e) {
        e.preventDefault();
        let contentId = $(this).data("id");
        deleteContent(contentId);
    });

    $('#btn-bulk-delete').on('click', function(e){
      e.preventDefault();
      bulkDelete(selectedIds);
    });

    $('#btn-close-banner').on('click', function(){
      $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
      $(".multiple-select").removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
      $(".unchecked").show();
      $(".checked").hide();
      selectedIds = [];
      isMultipleSelect = false;
      selectionBanner.hide();
      if(smScreen) pageTitle.show();
      detailContent.html(emptyContent);
    });

    $('#btn-save').on('click', function(e){
      Swal.fire({
        title: "Are you sure?",
        text: "Your data will saved as draft.",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, save it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $('#update-type').val('update')
          btnLoading.addClass("flex").removeClass("hidden");
          btnUpdate.removeClass("flex").addClass("hidden");
          detailContentForm.submit()
        }
      });
    });

    $('#btn-submit').on('click', function(){
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, submit it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $('#update-type').val('submit')
          btnLoading.addClass("flex").removeClass("hidden");
          btnUpdate.removeClass("flex").addClass("hidden");
          detailContentForm.submit()
        }
      });
    });

    // Add window resize event to adjust banner position
    $(window).on('resize', function() {
      if (isMultipleSelect) {
        adjustBannerPosition();
      }
      smScreen = window.matchMedia("(max-width: 991px)").matches;
    });

    // Simplified sidebar navigation with filter preservation
    $('#submit-content-submenu a').on('click', function(e) {
      if (window.location.pathname.includes('/submit-content')) {
        const currentFilter = new URLSearchParams(window.location.search).get('filter') || 'index';
        const url = new URL($(this).attr('href'), window.location.origin);
        url.searchParams.set('filter', currentFilter);
        window.location.href = url.toString();
      }
    });
  });
</script>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/components/main-scripts.blade.php ENDPATH**/ ?>